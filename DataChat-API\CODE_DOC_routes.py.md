# 代码文档 - server/routes.py

## 文件作用
Flask应用的路由定义文件，提供知识库管理、文档处理、聊天对话等API接口。

## 逐行代码解释

### 导入模块 (1-46行)
```python
import os                         # 操作系统接口
import asyncio                    # 异步编程支持
import logging                    # 日志记录
import json                       # JSON数据处理
import base64                     # Base64编码
import queue                      # 队列数据结构
import requests                   # HTTP请求库
import threading                  # 线程支持
from functools import partial     # 偏函数
from uuid import uuid4            # UUID生成
from time import time             # 时间戳
from datetime import datetime     # 日期时间
from urllib.parse import quote, unquote  # URL编码解码

from flask import (               # Flask框架组件
    request, session, jsonify, Response, 
    copy_current_request_context, redirect, 
    make_response, send_file
)
from app import app               # Flask应用实例

from models import (              # 数据模型和操作函数
    ObjID, User, Collection, Documents, Embedding,  # 数据模型
    get_user, save_user,          # 用户操作
    get_collections, get_collection_by_id,  # 集合操作
    get_collection_id_by_hash, get_hash_by_collection_id,
    get_data_by_hash, save_collection,
    update_collection_by_id, delete_collection_by_id,
    get_documents_by_collection_id, remove_document_by_id,  # 文档操作
    query_by_collection_id, chat_on_collection,  # 查询和聊天
    get_bot_list, get_bot_by_hash, create_bot, update_bot_by_hash,  # 机器人操作
    query_by_document_id, get_docs_by_document_id,
    purge_document_by_id, get_document_id_by_uniqid,
    set_document_summary, get_document_by_id,
    get_relation_count_by_id,
)

from celery_app import embed_documents, get_status_by_id, embed_feishuwiki  # Celery任务
from sse import ServerSentEvents  # 服务端推送
from tasks import LarkDocLoader, YuqueDocLoader, NotionDocLoader, LarkWikiLoader  # 文档加载器
```

### 异常类定义 (49-51行)
```python
class InternalError(Exception): pass      # 内部错误异常
class PermissionDenied(Exception): pass  # 权限拒绝异常
class NeedAuth(Exception): pass          # 需要认证异常
```

### 访问令牌创建函数 (54-64行)
```python
def create_access_token(user):
    """创建用户访问令牌"""
    extra = user.extra.to_dict()  # 获取用户额外信息
    app.logger.info("extra %r %r", extra, dict(extra))
    
    # 兼容两种权限格式：<has_privilege, expires>和<active, exp_time>
    expires = extra.get('exp_time', extra.get('permission', {}).get('expires', 0))
    privilege = extra.get('active', extra.get('permission', {}).get('has_privilege', False))
    
    app.logger.debug("create_access_token %r expires %r time %r", user.extra, expires, time())
    
    # 检查权限和过期时间
    if privilege and expires > time():
        return session.sid, int(expires)  # 返回会话ID和过期时间
    raise PermissionDenied()  # 权限不足或已过期
```

### 请求后处理中间件 (67-78行)
```python
@app.after_request
def after_request_callback(response):
    """请求完成后的日志记录"""
    app.logger.info(
        "%s - %s %s %s %s %sms",
        request.remote_addr,           # 客户端IP
        request.method,                # HTTP方法
        request.path,                  # 请求路径
        response.status_code,          # 响应状态码
        response.content_length,       # 响应内容长度
        int((time() - request.environ['REQUEST_TIME']) * 1000),  # 请求耗时(毫秒)
    )
    return response
```

### 请求前处理中间件 (81-108行)
```python
@app.before_request 
def before_request_callback():
    """请求前的认证和权限检查"""
    request.environ['REQUEST_TIME'] = time()  # 记录请求开始时间
    
    # 白名单路径，无需认证
    if request.path in [
        '/api/access_token',
        '/api/login', '/login', '/api/code2session',
        '/', '/favicon.ico',
        '/apispec_1.json', '/apidocs/'
    ]:
        return
    
    # Swagger静态资源路径
    if 'flasgger_static' in request.path:
        return
    
    # 嵌入式聊天接口，使用hash验证而非session
    if '/embed' in request.path and '/chat/completions' in request.path:
        return
    
    # 文件下载接口
    if '/api/file' in request.path:
        return
    
    # 检查会话认证信息
    access_token = session.get('access_token', '')
    expired = session.get('expired', 0)
    user_id = session.get('user_id', '')
    
    if access_token and user_id:
        if expired > time():
            pass  # 认证有效
        else:
            raise PermissionDenied()  # 会话已过期
    else:
        raise NeedAuth()  # 需要认证
```

### 模拟登录页面 (111-141行)
```python
@app.route('/login', methods=['GET', 'POST'])
def login_form():
    """模拟客户登录页面"""
    if request.method == 'GET':
        # 返回简单的HTML登录表单
        return '''
<h1>登录</h1>
<form action="/login" method="post">
  <input name="name" /><br />
  <input name="passwd" type="password" /><br />
  <button type="submit">登录</button>
</form>
    '''
    elif request.method == 'POST':
        name = request.form.get('name')
        passwd = request.form.get('passwd')
        app.logger.info("debug %r", (name, passwd))
        
        # 模拟登录逻辑（不验证密码）
        user = {
            'name': name,
            'openid': base64.urlsafe_b64encode(name.encode()).decode(),  # 生成openid
            'permission': {
                'has_privilege': True,      # 拥有权限
                'expires': time() + 3600,   # 1小时后过期
            }
        }
        
        # 将用户信息编码为code参数
        code = base64.b64encode(json.dumps(user).encode()).decode()
        return redirect('{}/api/login?code={}'.format(app.config['DOMAIN'], code))
```

### 基础路由 (143-149行)
```python
@app.route('/favicon.ico', methods=['GET'])
def faviconico():
    """网站图标接口"""
    return ''

@app.route('/', methods=['GET'])
def home():
    """首页接口"""
    return '<h1>首页</h1><a href="/api/login">登录</a>'
```

### code2session接口 (151-160+行)
```python
@app.route('/api/code2session', methods=['GET'])
def code2session():
    """
    code2session - 外部系统集成接口
    将授权码转换为会话信息
    
    参数:
      - code: 授权码（Base64编码的用户信息）
    """
    # 这是Swagger文档注释的开始
    # 实际实现会解析code参数并创建用户会话
```

## 技术特点

### 认证授权
- **会话管理**: 基于Flask session的用户会话
- **权限检查**: 支持权限和过期时间验证
- **白名单机制**: 部分接口无需认证
- **多种认证方式**: 支持session和hash两种认证

### 中间件系统
- **请求日志**: 自动记录请求信息和耗时
- **统一认证**: 请求前统一进行权限检查
- **异常处理**: 统一的异常类型定义

### 外部集成
- **模拟登录**: 提供简单的登录页面用于测试
- **code2session**: 支持外部系统的单点登录集成
- **灵活配置**: 支持多种权限格式兼容

### API设计
- **RESTful**: 遵循REST API设计规范
- **Swagger文档**: 自动生成API文档
- **多格式支持**: 支持JSON、文件下载等多种响应格式
