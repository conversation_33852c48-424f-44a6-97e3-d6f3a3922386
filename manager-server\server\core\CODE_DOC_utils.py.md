# 代码文档 - server/core/utils.py

## 文件作用
核心工具函数库，提供数据转换、类型验证、国际化、加密解密、图片处理等通用功能。

## 逐行代码解释

### 导入模块 (1-33行)
```python
import re                                # 正则表达式
import urllib                            # URL处理
import warnings                          # 警告处理
import asyncio                           # 异步编程
import os, stat                          # 操作系统接口
import json                              # JSON处理
import base64                            # Base64编码
import logging                           # 日志记录
import bcrypt                            # 密码哈希
import random                            # 随机数生成
import string                            # 字符串操作
import subprocess                        # 子进程
import httpx                             # HTTP客户端
import tornado.locale                    # Tornado国际化
from functools import partial            # 偏函数
from copy import deepcopy                # 深拷贝
from os.path import join, dirname        # 路径操作
from tempfile import NamedTemporaryFile  # 临时文件
from tornado.options import options      # Tornado配置
from datetime import datetime, time      # 日期时间
from decimal import Decimal              # 精确小数
from sqlalchemy.orm.collections import InstrumentedList  # SQLAlchemy集合
from sqlalchemy.orm.attributes import QueryableAttribute, InstrumentedAttribute  # SQLAlchemy属性
from sqlalchemy.orm.base import instance_state  # SQLAlchemy实例状态

from .downloader import ChunkedDownloader  # 分块下载器
from .schema import Base, bson             # 数据模型和BSON
from .exception import ParametersError     # 参数错误异常
from PIL import Image, ImageFont, ImageDraw  # 图片处理
from Crypto.Cipher import AES              # AES加密
from typing import Dict, Any               # 类型提示
from io import BytesIO                     # 字节流
```

### 国际化初始化 (34-49行)
```python
warnings.filterwarnings("ignore", category=DeprecationWarning)  # 忽略废弃警告

# 加载国际化翻译文件
i18n_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'locales')
tornado.locale.load_translations(i18n_path)  # 加载翻译
tornado.locale.set_default_locale('zh_CN')   # 设置默认语言为中文

def _(text, lang=options.DEFAULT_LOCALE, **kwargs):
    """国际化翻译函数"""
    user_locale = tornado.locale.get(lang)  # 获取用户语言环境
    
    if '：' in text:
        # 处理包含中文冒号的文本
        text = user_locale.translate(text) % kwargs
        return '：'.join([user_locale.translate(t) % kwargs for t in text.split('：')])
    
    if '\n' in text:
        # 处理多行文本
        return '\n'.join([user_locale.translate(t) % kwargs for t in text.split('\n')])
    
    return user_locale.translate(text) % kwargs  # 普通翻译
```

### row2dict函数 - 对象转字典 (52-96行)
```python
def row2dict(row, lang=''):
    """将对象(一般为ORM row)转换为dict"""
    record = {}
    
    # 清除过期状态，避免懒加载问题
    state = instance_state(row)
    state.expired_attributes.clear()
    
    # 深拷贝属性并获取类信息
    attributes, cls = deepcopy(row.__dict__), row.__class__
    
    # 处理hybrid_property属性
    for c in dir(row):
        if hasattr(cls, c):
            a = getattr(cls, c)
            # 检查是否为QueryableAttribute但不是InstrumentedAttribute
            if isinstance(a, QueryableAttribute) and not isinstance(a, InstrumentedAttribute):
                attributes[c] = 1  # 标记为需要处理的属性

    # 遍历所有属性进行转换
    for c in attributes:
        if not c.startswith('_') and 'metadata' != c:  # 跳过私有属性和metadata
            try:
                v = row.__getattribute__(c)  # 获取属性值
            except KeyError as e:
                logging.exception(e)
                # 为时间字段提供默认值
                v = datetime.now() if c in ['created', 'modified'] else None
            
            # 根据值类型进行转换
            if isinstance(v, Base):
                v = row2dict(v, lang=lang)  # 递归处理嵌套对象
            elif isinstance(v, Decimal):
                v = int(v)  # Decimal转整数
            elif c in ['start', 'end'] and row.__tablename__ in ['work', 'education']:
                v = v.strftime('%Y-%m')  # 工作/教育经历的开始结束时间
            elif c in ['birthday'] and row.__tablename__ in ['user']:
                v = v.strftime('%Y-%m-%d')  # 用户生日
            elif c in ['account_type'] and row.__tablename__ in ["admin_user"]:
                v = v.split(",")  # 账户类型分割为数组
            elif isinstance(v, datetime):
                v = v.strftime('%Y-%m-%d %H:%M:%S')  # 日期时间格式化
            elif isinstance(v, time):
                v = v.isoformat()  # 时间ISO格式
            elif isinstance(v, InstrumentedList):
                v = list(map(lambda i: row2dict(i, lang=lang), v))  # 处理关联对象列表
            elif isinstance(v, str) and '%' not in v:
                v = _(v, lang=lang)  # 翻译字符串字段
            
            record[c] = v

    return record
```

### 工具函数 (99-107行)
```python
def chunks(l, n):
    """将列表分割为n大小的块"""
    for i in range(0, len(l), n):
        yield l[i:i + n]

def format_time(t):
    """格式化时间为标准字符串"""
    return t.strftime('%Y-%m-%d %H:%M:%S')
```

### 自定义字符串类型 (109-152行)
```python
class DateTimeStr(str):
    """日期时间字符串类型 - 验证格式并转换"""
    def __new__(cls, value, **kwargs):
        try:
            return datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logging.exception(e)
            raise ParametersError(value)

class TimeStr(str):
    """时间字符串类型 - 支持两种格式"""
    def __new__(cls, value, **kwargs):
        try:
            return datetime.strptime(value, '%H:%M')  # 尝试HH:MM格式
        except Exception as e:
            try:
                return datetime.strptime(value, '%H:%M:%S')  # 尝试HH:MM:SS格式
            except Exception as e:
                logging.exception(e)
                raise ParametersError(value)

class DateStr(str):
    """日期字符串类型 - 验证YYYY-MM-DD格式"""
    def __new__(cls, value, **kwargs):
        try:
            datetime.strptime(value, '%Y-%m-%d')
        except Exception as e:
            logging.exception(e)
            raise ParametersError(value)
        return str(value, **kwargs)

class ObjIDStr(str):
    """ObjectID字符串类型 - 验证MongoDB ObjectID格式"""
    def __new__(cls, value, **kwargs):
        if not bson.ObjectId.is_valid(value):
            raise ParametersError(value)
        return str(value, **kwargs)
```

### JSON编码器 (154-160+行)
```python
class ExtendJSONEncoder(json.JSONEncoder):
    """扩展JSON编码器 - 处理特殊类型"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            # Decimal类型转换
            return int(obj) if obj == obj.to_integral_value() else float(obj)
        elif isinstance(obj, datetime):
            return format_time(obj)  # 日期时间格式化
        # 其他类型使用默认处理
        return super().default(obj)
```

## 技术特点

### 数据转换
- **ORM对象转换**: 将SQLAlchemy对象转换为字典
- **类型安全**: 自定义字符串类型确保数据格式正确
- **递归处理**: 支持嵌套对象的深度转换
- **特殊字段处理**: 针对不同表的特殊字段进行定制转换

### 国际化支持
- **多语言**: 支持基于Tornado的国际化机制
- **智能翻译**: 处理包含特殊字符的文本翻译
- **字段翻译**: 自动翻译数据库字段值

### 参数验证
- **格式验证**: 日期、时间、ObjectID格式验证
- **异常处理**: 统一的参数错误异常处理
- **类型转换**: 字符串到特定类型的安全转换

### 工具函数
- **分块处理**: 将大列表分割为小块处理
- **时间格式化**: 统一的时间格式化函数
- **JSON序列化**: 支持特殊类型的JSON编码

## 使用场景
- **API响应**: 将数据库对象转换为API响应格式
- **参数验证**: 验证HTTP请求参数的格式
- **国际化**: 多语言应用的文本翻译
- **数据处理**: 批量数据的分块处理
