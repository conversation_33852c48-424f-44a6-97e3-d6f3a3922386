# 代码文档 - contents/deploy.tsx

## 文件作用
浏览器扩展的内容脚本，在网页中注入自动部署功能，提供一键部署ConnectAI应用到飞书平台的能力。

## 逐行代码解释

### 导入模块和类型定义 (1-11行)
```typescript
// @ts-nocheck                           // 禁用TypeScript类型检查
import { useCallback, useEffect, useMemo, useState } from "react";  // React Hooks
import { sendToBackground } from "@plasmohq/messaging";  // Plasmo消息传递
import { FEISHU_DOMAIN as domain } from '~utils/browser'  // 飞书域名配置
import cssText from "data-text:~/contents/deploy.css";   // CSS样式文本
import type {                            // Plasmo类型定义
  PlasmoCSConfig, PlasmoGetOverlayAnchor, PlasmoWatchOverlayAnchor
} from "plasmo";
import { Toast } from "@douyinfe/semi-ui";  // Semi UI Toast组件
```

### Plasmo配置 (13-31行)
```typescript
export const config: PlasmoCSConfig = {
  matches: ["<all_urls>"]                // 匹配所有URL，在所有网站注入
};

// 注入到ShadowDOM的样式
export const getStyle = () => {
  const style = document.createElement("style");
  style.textContent = cssText;           // 设置CSS内容
  return style;
};

// 监听覆盖层锚点位置变化
export const watchOverlayAnchor: PlasmoWatchOverlayAnchor = (updatePosition) => {
  const interval = setInterval(() => {
    updatePosition();                    // 每10ms更新一次位置
  }, 10);
  return () => clearInterval(interval);  // 返回清理函数
};
```

### 通知函数 (33-39行)
```typescript
const notify = () => {
  Toast.warning({
    content: "当前租户没有更新应用的权限，请手动部署",  // 警告内容
    duration: 3,                         // 显示3秒
    zIndex: 9999                         // 高层级显示
  });
};
```

### 覆盖层锚点获取 (42-43行)
```typescript
export const getOverlayAnchor: PlasmoGetOverlayAnchor = async () =>
  document.querySelector(`.connectai-auto-deploy`) as any;  // 查找部署按钮元素
```

### AutoDeploy主组件 (44-73行)
```typescript
const AutoDeploy = ({ anchor }) => {
  // 状态管理
  const [loading, setLoading] = useState(false);    // 加载状态
  const [success, setSuccess] = useState(false);    // 成功状态
  const [style, setStyle] = useState({});           // 样式状态
  const [state, setState] = useState({});           // 应用状态
  const [user, setUser] = useState({});             // 用户信息

  useEffect(() => {
    // 获取锚点元素的位置和大小信息
    const r = anchor.element.getBoundingClientRect().toJSON();
    setTimeout(() => {
      setStyle({ width: r?.width, height: r?.height });  // 设置覆盖层大小
    }, 400);
  });

  useEffect(() => {
    // 从锚点元素的dataset获取应用配置信息
    setState(anchor.element.dataset);
    
    // 向后台脚本请求用户信息
    sendToBackground({ name: "user-info", body: { domain } }).then(({ user }) => {
      console.log("userInfo", user);
      if (user) {
        setUser(user);
      }
    }).catch(e => {
      console.log("userInfo", e);
    });
  }, []);
```

### saveBot函数 - 保存机器人配置 (75-108行)
```typescript
const saveBot = useCallback(async (app) => {
  const {
    encryptKey, secret, verificationToken, appId,
    id, botId, description, name, saveBotUrl,
  } = app;
  
  // 构建API URL
  let url = saveBotUrl ? saveBotUrl : `/api/app/${id}/client/${botId}`;
  if (window.location.hostname.indexOf("localhost") > -1) {
    url = "/api" + url;  // 本地开发环境添加/api前缀
  }
  
  // 发送POST请求保存机器人配置
  fetch(url, {
    method: "POST",
    body: JSON.stringify({
      name, description,
      app_id: appId,
      app_secret: secret
      // encript_key: encryptKey,      // 注释掉的加密密钥
      // validation_token: verificationToken,  // 注释掉的验证令牌
    }),
    mode: "cors",
    credentials: "include",              // 包含认证信息
    headers: {
      "content-type": "application/json"
    }
  }).then(res => {
    console.log("saveBot", res);
  });
}, [state]);
```

### handleDeploy函数 - 处理部署逻辑 (110-160+行)
```typescript
const handleDeploy = useCallback(async (e) => {
  if (loading) {
    return;  // 防止重复点击
  }
  
  if (success) {
    // 部署成功后的处理
    const button = document.querySelector(`.connectai-auto-deploy-close`);
    if (button) {
      button.click();  // 关闭部署弹窗
    }
    setTimeout(() => {
      // 跳转到飞书应用审核页面
      window.open("https://feishu.cn/admin/appCenter/audit");
    }, 1000);
    return;  // 防止重复创建机器人
  }
  
  setLoading(true);
  try {
    console.log("deploy", state, user);
    const {
      appId, botId, id, cardCallback, eventCallback,
      name, description, logo, encryptKey, verificationToken, saveBotUrl
    } = state;
    
    // 验证AppID是否有效
    const ifValid = validateAppId(appId);
    
    if (ifValid) {
      // 获取应用信息
      const app = await sendToBackground({ 
        name: "get-app-info", 
        body: { appId, domain } 
      });
      
      console.log("app", app.error);
      if (app.error) {
        notify();  // 显示权限错误通知
        throw new Error(app.error);
      }
      
      // 保存机器人配置
      await saveBot({ ...app, id, appId, botId, name, description, saveBotUrl });
      
      // 发布应用
      const resp = await sendToBackground({
        name: "publish-app",
        body: { appId, eventCallback, cardCallback, encryptKey, verificationToken, domain },
      });
      
      console.log("resp", resp);
      if (resp.response) {
        setSuccess(true);  // 设置成功状态
      }
    } else {
      // 创建新应用并获取应用信息
      // ... 创建应用的逻辑
    }
  } catch (error) {
    console.error("部署失败:", error);
  } finally {
    setLoading(false);
  }
}, [state, user, loading, success, saveBot]);
```

## 技术特点

### Plasmo框架集成
- **内容脚本**: 作为浏览器扩展的内容脚本注入到网页
- **覆盖层**: 使用Plasmo的覆盖层机制在页面上显示UI
- **消息传递**: 通过sendToBackground与后台脚本通信
- **样式隔离**: 使用ShadowDOM隔离样式

### React组件化
- **Hooks使用**: 使用useState、useEffect、useCallback等Hooks
- **状态管理**: 管理加载、成功、样式、应用状态等多个状态
- **事件处理**: 处理用户交互和异步操作

### 飞书API集成
- **应用管理**: 获取和创建飞书应用
- **机器人配置**: 配置机器人的回调地址和参数
- **应用发布**: 自动发布应用到飞书平台
- **审核跳转**: 部署成功后跳转到审核页面

### 错误处理和用户体验
- **权限检查**: 检查用户是否有部署权限
- **加载状态**: 显示加载状态防止重复操作
- **成功反馈**: 部署成功后的视觉反馈
- **错误通知**: 使用Toast显示错误信息

## 部署流程
1. **用户点击部署**: 用户在网页上点击自动部署按钮
2. **获取配置**: 从页面元素获取应用配置信息
3. **验证权限**: 检查用户是否有部署权限
4. **获取应用信息**: 从飞书API获取应用详细信息
5. **保存机器人**: 将机器人配置保存到ConnectAI平台
6. **发布应用**: 调用飞书API发布应用
7. **跳转审核**: 成功后跳转到飞书审核页面

## 使用场景
- **一键部署**: 简化ConnectAI应用到飞书的部署流程
- **配置同步**: 自动同步应用配置到飞书平台
- **开发效率**: 提升开发者的部署效率
- **错误处理**: 提供友好的错误提示和处理
