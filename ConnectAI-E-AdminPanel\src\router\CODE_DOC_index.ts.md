# 代码文档 - src/router/index.ts

## 文件作用
Vue Router路由系统的主配置文件，负责创建路由实例、配置路由模式、设置路由守卫等核心功能。

## 逐行代码解释

### 导入模块 (1-7行)
```typescript
import type { App } from 'vue';                                    // Vue应用类型
import { createRouter, createWebHashHistory, createWebHistory } from 'vue-router';  // Vue Router核心函数
import { transformRouteNameToRoutePath } from '@/utils';           // 路由名称转路径工具
import { transformAuthRouteToVueRoutes } from '@/utils/router/transform';  // 权限路由转换工具
import { constantRoutes } from './routes';                         // 常量路由配置
import { scrollBehavior } from './helpers';                       // 滚动行为配置
import { createRouterGuard } from './guard';                      // 路由守卫创建函数
```

### 环境变量配置 (9行)
```typescript
const { VITE_HASH_ROUTE = 'N', VITE_BASE_URL } = import.meta.env;
```
**说明**:
- `VITE_HASH_ROUTE`: 控制是否使用Hash路由模式，默认为'N'（不使用）
- `VITE_BASE_URL`: 应用的基础URL路径

### 路由实例创建 (11-15行)
```typescript
export const router = createRouter({
  // 根据环境变量选择路由模式
  history: VITE_HASH_ROUTE === 'Y' ? createWebHashHistory(VITE_BASE_URL) : createWebHistory(VITE_BASE_URL),
  // 将权限路由转换为Vue Router路由配置
  routes: transformAuthRouteToVueRoutes(constantRoutes),
  // 配置页面滚动行为
  scrollBehavior
});
```
**说明**:
- **路由模式选择**: 
  - Hash模式 (`createWebHashHistory`): URL包含#，兼容性好
  - History模式 (`createWebHistory`): 现代浏览器的标准模式
- **路由转换**: 将自定义的权限路由格式转换为Vue Router标准格式
- **滚动行为**: 配置页面切换时的滚动位置处理

### setupRouter异步函数 (17-22行)
```typescript
/** setup vue router. - [安装vue路由] */
export async function setupRouter(app: App) {
  app.use(router);              // 在Vue应用中注册路由器
  createRouterGuard(router);    // 创建路由守卫（权限控制、页面拦截等）
  await router.isReady();       // 等待路由器准备就绪
}
```
**说明**:
- **路由注册**: 将路由器实例注册到Vue应用中
- **守卫创建**: 设置路由守卫，处理权限验证、登录拦截等
- **异步等待**: 确保路由器完全初始化后再继续

### 工具函数导出 (24-27行)
```typescript
/** 路由名称 */
export const routeName = (key: AuthRoute.AllRouteKey) => key;

/** 路由路径 */
export const routePath = (key: Exclude<AuthRoute.AllRouteKey, 'not-found'>) => transformRouteNameToRoutePath(key);
```
**说明**:
- **routeName**: 路由名称获取函数，直接返回路由键名
- **routePath**: 路由路径获取函数，将路由名称转换为实际路径
- **类型安全**: 使用TypeScript类型确保路由键的正确性，排除'not-found'路由

### 模块导出 (29-31行)
```typescript
export * from './routes';      // 导出所有路由配置
export * from './modules';     // 导出所有路由模块
```

## 技术特点

### 路由模式支持
- **双模式支持**: 同时支持Hash和History两种路由模式
- **环境配置**: 通过环境变量动态选择路由模式
- **兼容性考虑**: Hash模式适用于不支持History API的环境

### 权限路由系统
- **路由转换**: 自定义权限路由格式到Vue Router标准格式的转换
- **类型安全**: 完整的TypeScript类型定义
- **守卫机制**: 统一的路由守卫处理权限控制

### 异步初始化
- **异步设置**: 支持异步的路由器初始化过程
- **就绪等待**: 确保路由器完全准备就绪后再使用
- **错误处理**: 支持初始化过程中的错误处理

### 工具函数
- **路径转换**: 路由名称到路径的自动转换
- **类型约束**: 使用TypeScript类型系统确保路由操作的安全性
- **开发便利**: 提供便捷的路由操作工具函数

## 使用场景
- **应用初始化**: 在main.ts中调用setupRouter初始化路由系统
- **路由跳转**: 使用routeName和routePath函数进行类型安全的路由操作
- **权限控制**: 通过路由守卫实现页面级别的权限控制
- **多环境部署**: 根据部署环境选择合适的路由模式

## 配置说明
- **VITE_HASH_ROUTE='Y'**: 使用Hash路由模式，适用于静态文件部署
- **VITE_HASH_ROUTE='N'**: 使用History路由模式，需要服务器支持
- **VITE_BASE_URL**: 设置应用的基础路径，用于子目录部署
