# 代码文档 - vite.config.ts

## 文件作用
Vite构建工具的主配置文件，定义开发服务器、构建选项、插件配置等。

## 核心配置

### 环境变量处理
- **loadEnv()**: 加载环境变量
- **getServiceEnvConfig()**: 获取服务环境配置
- **isOpenProxy**: 是否开启代理 (VITE_HTTP_PROXY)
- **isLark**: 是否为飞书版本 (VITE_IS_LARK)

### 路径别名
- **~**: 项目根目录
- **@**: src源码目录
- **vue-i18n**: 指定Vue国际化模块路径

### 开发服务器
- **host**: 0.0.0.0 (允许外部访问)
- **port**: 3200
- **open**: 自动打开浏览器
- **proxy**: 动态代理配置

### CSS预处理
- **SCSS**: 全局样式自动导入
- **additionalData**: 自动注入全局SCSS变量

### 依赖优化
预构建的第三方库:
- **@antv/data-set**: 数据处理
- **@antv/g2**: 数据可视化
- **@better-scroll/core**: 滚动组件
- **swiper**: 轮播组件
- **vditor**: Markdown编辑器
- **wangeditor**: 富文本编辑器
- **xgplayer**: 视频播放器

### 构建配置
- **reportCompressedSize**: 禁用压缩大小报告
- **sourcemap**: 禁用源码映射
- **commonjsOptions**: CommonJS处理选项

### 多入口配置
- **普通版本**: index.html
- **飞书版本**: index_en.html (英文版)

### 代码分割
- **manualChunks**: 手动分割代码块
- **virtual-svg-icons**: SVG图标单独打包

## 技术特点
- 支持多环境配置
- 飞书定制版本支持
- 智能代理配置
- 依赖预构建优化
- 灵活的代码分割
