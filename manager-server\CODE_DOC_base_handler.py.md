# 代码文档 - server/core/base_handler.py

## 文件作用
Web请求处理器基类，提供会话管理、用户认证、权限控制等核心功能。

## 核心类

### WebSession
- **继承**: dict
- **功能**: 会话数据容器
- **特性**:
  - `dirty` 标记: 跟踪数据变更
  - 支持属性访问语法
  - 自动标记修改状态

### SessionHandler
- **继承**: tornado.web.RequestHandler
- **功能**: 会话管理基类

#### 会话管理
- **session_id**: 从头部/Cookie/参数获取会话ID
- **_get_current_user_from_redis()**: 从Redis获取用户信息
- **get_current_user()**: 获取当前用户ID
- **gen_session_id()**: 生成新会话ID
  - 7天过期时间
  - 存储到Redis和Cookie
  - UUID格式会话标识

#### 会话存储
- **Redis键格式**: `{namespace}:sid:{session_id}`
- **数据结构**: JSON格式用户信息
- **过期时间**: 7天自动清理

## 依赖模块
- **core.utils**: 工具函数
- **core.redisdb**: Redis客户端
- **core.base_model**: 数据模型基类
- **core.exception**: 自定义异常
- **core.schema**: 数据结构定义
- **core.mysql**: 数据库会话

## 异常处理
- **NotFound**: 资源未找到
- **ParametersError**: 参数错误
- **FileTypeError**: 文件类型错误
- **Duplicate**: 重复数据
- **PermissionDenied**: 权限拒绝
- **InternalError**: 内部错误

## 技术特点
- 基于Redis的分布式会话
- 支持多种会话ID传递方式
- 自动会话过期管理
- 统一的异常处理机制
- 支持国际化
