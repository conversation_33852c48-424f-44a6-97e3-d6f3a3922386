# 代码文档 - server/core/exception.py

## 文件作用
定义应用层统一的异常类体系，提供标准化的错误处理机制。

## 逐行代码解释

### InternalError基础异常类 (1-6行)
```python
class InternalError(Exception):
    """应用层统一的异常"""
    def __init__(self, msg='内部错误', code=-1):
        self.code = code    # 错误代码，默认-1
        self.msg = msg      # 错误消息，默认"内部错误"
```
**说明**: 
- 继承自Python内置Exception类
- 提供统一的错误代码和消息格式
- 作为所有业务异常的基类

### NotFound异常类 (8-10行)
```python
class NotFound(InternalError):
    """未找到指定项"""
```
**说明**: 
- 继承自InternalError
- 用于表示资源未找到的情况
- 如：用户不存在、应用不存在等

### FileTypeError异常类 (12-14行)
```python
class FileTypeError(InternalError):
    """文件类型错误"""
```
**说明**: 
- 继承自InternalError
- 用于文件上传时类型不匹配的情况
- 如：上传了不支持的文件格式

### Duplicate异常类 (16-22行)
```python
class Duplicate(InternalError):
    """重复项"""
    
    def __init__(self, msg="已存在", code=-1, duplicate=None):
        self.duplicate = duplicate  # 存储重复的对象信息
        super().__init__(msg, code) # 调用父类构造函数
```
**说明**: 
- 继承自InternalError
- 用于表示数据重复的情况
- 额外提供duplicate属性存储重复对象的详细信息
- 默认错误消息为"已存在"

### PermissionDenied异常类 (24-26行)
```python
class PermissionDenied(InternalError):
    """拒绝授权"""
```
**说明**: 
- 继承自InternalError
- 用于权限验证失败的情况
- 如：用户无权限访问某个资源

### ParametersError异常类 (28-30行)
```python
class ParametersError(InternalError):
    """参数错误"""
```
**说明**: 
- 继承自InternalError
- 用于请求参数验证失败的情况
- 如：必填参数缺失、参数格式错误等

### InvalidEventException异常类 (32-34行)
```python
class InvalidEventException(InternalError):
    """参数错误"""
```
**说明**: 
- 继承自InternalError
- 用于无效事件的情况
- 注释与类名不完全匹配，可能是事件处理相关的异常

### SensitiveError异常类 (36-38行)
```python
class SensitiveError(InternalError):
    """匹配到敏感词"""
```
**说明**: 
- 继承自InternalError
- 用于内容安全检查失败的情况
- 当用户输入包含敏感词汇时抛出

## 异常体系结构
```
Exception (Python内置)
    └── InternalError (应用基础异常)
        ├── NotFound (资源未找到)
        ├── FileTypeError (文件类型错误)
        ├── Duplicate (数据重复)
        ├── PermissionDenied (权限拒绝)
        ├── ParametersError (参数错误)
        ├── InvalidEventException (无效事件)
        └── SensitiveError (敏感词检测)
```

## 使用场景
- **API接口**: 统一的错误响应格式
- **业务逻辑**: 明确的异常类型区分
- **错误处理**: 基于异常类型的不同处理策略
- **日志记录**: 结构化的错误信息记录

## 技术特点
- **继承体系**: 清晰的异常继承关系
- **统一格式**: 所有异常都包含code和msg属性
- **扩展性**: 易于添加新的异常类型
- **语义化**: 异常类名直接表达错误类型
