# 项目代码文档说明

## 文档命名规范
所有代码文档文件都以 `CODE_DOC_` 为前缀，便于识别和管理。

## Git忽略配置
已在以下位置配置了 `.gitignore` 规则，确保文档文件不被Git跟踪：
- 根目录: `.gitignore`
- manager-server: `manager-server/.gitignore`
- ConnectAI-E-AdminPanel: `ConnectAI-E-AdminPanel/.gitignore`
- DataChat-API: `DataChat-API/.gitignore`
- ConnectAI-Helper: `ConnectAI-Helper/.gitignore`

## 已创建的文档列表

### 根目录文档
- `CODE_DOC_README.md` - 项目总体说明
- `CODE_DOC_Makefile.md` - 构建脚本说明
- `CODE_DOC_release.md` - 版本发布记录

### manager-server 项目文档
- `CODE_DOC_README.md` - 项目概览
- `CODE_DOC_server.py.md` - 主服务入口
- `CODE_DOC_api.py.md` - API路由加载器
- `CODE_DOC_config.py.md` - 配置管理
- `CODE_DOC_base_handler.py.md` - 请求处理基类

### ConnectAI-E-AdminPanel 项目文档
- `CODE_DOC_README.md` - 项目概览
- `CODE_DOC_package.json.md` - 项目配置
- `CODE_DOC_App.vue.md` - 根组件
- `CODE_DOC_vite.config.ts.md` - 构建配置

### DataChat-API 项目文档
- `CODE_DOC_README.md` - 项目概览
- `CODE_DOC_app.py.md` - Flask应用配置

### ConnectAI-Helper 项目文档
- `CODE_DOC_README.md` - 项目概览
- `CODE_DOC_package.json.md` - 扩展配置

### 部署配置文档
- `deploy/CODE_DOC_docker-compose.yml.md` - 容器编排配置

## 文档内容结构
每个文档都包含以下标准结构：
1. **文件作用** - 文件的主要功能和用途
2. **关键信息/组件** - 核心代码结构和重要组件
3. **技术特点** - 技术实现特色和亮点
4. **依赖关系** - 相关模块和依赖项

## 项目技术栈总览

### 后端技术
- **manager-server**: Python + Tornado + MySQL + Redis + RabbitMQ
- **DataChat-API**: Python + Flask + Elasticsearch + Celery

### 前端技术
- **ConnectAI-E-AdminPanel**: Vue3 + Vite + Naive UI + TypeScript
- **ConnectAI-Helper**: React + Plasmo + Semi UI + TypeScript

### 部署技术
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **消息队列**: RabbitMQ
- **搜索引擎**: Elasticsearch

## 使用建议
1. 查看项目概览文档了解整体架构
2. 根据需要查看具体文件的详细文档
3. 文档会随代码更新而需要同步更新
4. 建议在修改重要文件时同步更新对应文档
