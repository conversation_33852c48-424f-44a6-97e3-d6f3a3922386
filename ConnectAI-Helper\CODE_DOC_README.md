# 代码文档 - ConnectAI-Helper 项目概览

## 项目作用
企联AI助手浏览器扩展，提供一键部署ConnectAI应用的便捷工具，支持多平台机器人配置。

## 技术架构
- **扩展框架**: Plasmo 0.76.3
- **前端框架**: React 18.2.0
- **UI组件库**: Semi Design (字节跳动)
- **构建工具**: TypeScript + Tailwind CSS
- **代码编辑**: Ace Editor

## 目录结构

### 核心文件
- **popup/**: 扩展弹窗界面
  - index.tsx: 主弹窗组件
  - style.css: 弹窗样式

### 后台脚本
- **background/**: 后台服务
  - index.ts: 后台主脚本
  - messages/: 消息处理

### 内容脚本
- **contents/**: 页面注入脚本
  - batch_import.tsx: 批量导入功能
  - deploy.tsx: 部署功能
  - deploy_lark.tsx: 飞书部署
  - header/: 页面头部组件
  - mainContent/: 主要内容组件

### 组件库
- **components/**: 通用组件
  - configEditor/: 配置编辑器
  - icon/: 图标组件

### 工具模块
- **utils/**: 工具函数
  - browser.ts: 浏览器API封装
  - open-dingtalk-api/: 钉钉开放API
  - open-feishu-api/: 飞书开放API

### Hooks
- **hooks/**: React Hooks
  - useDeploy.tsx: 部署相关Hook

### 配置文件
- **constant.ts**: 常量定义
- **tailwind.config.js**: Tailwind配置
- **postcss.config.js**: PostCSS配置

## 核心功能

### 一键部署
- 快速配置机器人
- 自动生成配置文件
- 多平台支持
- 配置验证

### 平台集成
- **飞书**: 飞书机器人配置
- **钉钉**: 钉钉机器人配置
- **企业微信**: 企微机器人配置
- **通用API**: 标准API配置

### 配置管理
- 可视化配置编辑
- 配置模板
- 导入导出功能
- 配置验证

### 批量操作
- 批量导入配置
- 批量部署应用
- 配置同步
- 状态管理

## 浏览器支持
- **Chrome**: Manifest V3
- **Firefox**: Manifest V2  
- **Edge**: Manifest V3
- **跨平台**: 统一代码库

## 权限配置
- **所有URL访问**: 支持任意网站操作
- **脚本注入**: 页面功能增强
- **活动标签页**: 当前页面操作
- **Cookie访问**: 会话状态管理

## 开发特性
- **热重载**: 开发时实时更新
- **TypeScript**: 类型安全
- **组件化**: React组件开发
- **样式隔离**: CSS-in-JS方案
- **多目标构建**: 支持多浏览器

## 技术特点
- 现代化扩展开发
- 跨浏览器兼容
- 可视化配置界面
- 一键部署体验
- 多平台API集成
