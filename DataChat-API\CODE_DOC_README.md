# 代码文档 - DataChat-API 项目概览

## 项目作用
数据聊天API服务，基于Flask的知识库问答系统，集成Elasticsearch搜索引擎和多种AI模型。

## 技术架构
- **Web框架**: Flask + Gunicorn
- **搜索引擎**: Elasticsearch 8.9.0
- **任务队列**: Celery + Redis
- **会话存储**: Redis
- **部署**: Docker容器化

## 目录结构

### 核心服务
- **server/app.py**: Flask应用配置
- **server/server.py**: 主服务入口
- **server/celery_app.py**: Celery任务队列

### API模块
- **server/routes.py**: API路由定义
- **server/models.py**: 数据模型
- **server/sse.py**: 服务端推送
- **server/tasks.py**: 异步任务

### 部署配置
- **docker/**: Docker部署文件
  - Dockerfile: 容器构建
  - entrypoint.sh: 启动脚本
  - wait-for-it.sh: 依赖等待

### 项目配置
- **setup.py**: Python包配置
- **Makefile**: 构建脚本
- **docker-compose.yml**: 容器编排

## 核心功能

### 知识库管理
- 文档上传和解析
- 向量化存储
- 全文搜索
- 语义检索

### 对话系统
- 多轮对话支持
- 上下文理解
- 流式响应
- 会话管理

### AI模型集成
- OpenAI GPT系列
- Azure OpenAI
- 本地模型支持
- 多模型切换

### 文件处理
- 多格式文档支持
- 文本提取
- 分块处理
- 元数据管理

## API特性
- **RESTful设计**: 标准REST API
- **Swagger文档**: 自动生成API文档
- **流式响应**: 支持SSE实时推送
- **会话管理**: Redis分布式会话
- **CORS支持**: 跨域资源共享

## 部署特性
- **容器化**: Docker部署
- **微服务**: 独立服务部署
- **负载均衡**: 支持多实例
- **健康检查**: 服务状态监控
- **日志管理**: 统一日志收集

## 技术特点
- 高性能搜索引擎
- 异步任务处理
- 分布式会话管理
- 多格式文档支持
- 实时流式响应
