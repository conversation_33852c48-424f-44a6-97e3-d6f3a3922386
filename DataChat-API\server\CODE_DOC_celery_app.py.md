# 代码文档 - server/celery_app.py

## 文件作用
Celery异步任务定义文件，提供文档向量化、飞书知识库同步等后台任务处理功能。

## 逐行代码解释

### 导入模块 (1-17行)
```python
import os                                # 操作系统接口
import logging                           # 日志记录
import requests                          # HTTP请求库
from datetime import datetime            # 日期时间处理
from hashlib import md5                  # MD5哈希算法
from langchain.schema import Document    # LangChain文档类

from models import (                     # 数据模型和操作函数
    get_user, get_collection_by_id, Search,
    purge_document_by_id, get_document_id_by_uniqid
)
from tasks import (                      # 任务相关模块
    celery,                              # Celery应用实例
    SitemapLoader, LOADER_MAPPING,       # 文档加载器
    NamedTemporaryFile,                  # 临时文件
    embedding_single_document, get_status_by_id, embed_query,  # 向量化函数
    LarkWikiLoader, LarkDocLoader,       # 飞书加载器
    YuqueDocLoader, NotionDocLoader,     # 语雀和Notion加载器
)
```

### embed_documents任务 - 文档向量化 (20-102行)
```python
@celery.task()
def embed_documents(fileUrl, fileType, fileName, collection_id, openai=False, uniqid=None):
    """
    从URL获取文档并进行向量化处理
    
    参数:
        fileUrl: 文件URL地址
        fileType: 文件类型 (pdf, word, excel, markdown, ppt, txt, sitemap, feishudoc, yuque, notion)
        fileName: 文件名称
        collection_id: 知识库ID
        openai: 是否使用OpenAI向量化
        uniqid: 唯一标识符，默认使用URL的MD5值
    """
    # 生成文档唯一标识符
    uniqid = uniqid or md5(fileUrl.encode()).hexdigest()
    document_ids = []

    if fileType == 'sitemap':
        # 处理网站地图类型文档
        sitemap_loader = SitemapLoader(web_path=fileUrl)
        docs = sitemap_loader.load()                    # 加载网站地图中的所有页面
        for doc in docs:
            # 对每个页面进行向量化
            document_id = embedding_single_document(
                doc, fileUrl, fileType, fileName, collection_id, 
                openai=openai, uniqid=uniqid
            )
            document_ids.append(document_id)

    elif fileType in ['feishudoc']:
        # 处理飞书文档
        collection = get_collection_by_id(None, collection_id)
        user = get_user(collection.user_id)
        extra = user.extra.to_dict()
        client = extra.get('client', {})                # 获取飞书客户端配置
        
        loader = LarkDocLoader(fileUrl, None, **client)
        doc = loader.load()                             # 加载飞书文档
        document_id = embedding_single_document(
            doc, fileUrl, fileType,
            doc.metadata.get('title'),                  # 使用文档标题作为文件名
            collection_id,
            openai=openai,
            uniqid=doc.metadata.get('document_id'),     # 使用飞书文档ID作为唯一标识
            version=doc.metadata.get('revision_id'),    # 飞书文档版本号
        )
        document_ids.append(document_id)

    elif fileType in ['yuque']:
        # 处理语雀文档
        collection = get_collection_by_id(None, collection_id)
        user = get_user(collection.user_id)
        extra = user.extra.to_dict()
        yuque = extra.get('yuque', {})                  # 获取语雀配置
        
        loader = YuqueDocLoader(fileUrl, **yuque)
        doc = loader.load()
        document_id = embedding_single_document(
            doc, fileUrl, fileType,
            doc.metadata.get('title'),
            collection_id,
            openai=openai,
            uniqid=doc.metadata.get('uniqid'),
            version=0,                                   # 语雀文档版本固定为0
        )
        document_ids.append(document_id)

    elif fileType in ['notion']:
        # 处理Notion文档
        collection = get_collection_by_id(None, collection_id)
        user = get_user(collection.user_id)
        extra = user.extra.to_dict()
        notion = extra.get('notion', {})
        
        loader = NotionDocLoader(fileUrl, **client)     # 注意：这里可能是bug，应该用notion
        doc = loader.load()
        document_id = embedding_single_document(
            doc, fileUrl, fileType,
            doc.metadata.get('title'),
            collection_id,
            openai=openai,
            uniqid=doc.metadata.get('uniqid'),
            version=0
        )
        document_ids.append(document_id)

    elif fileType in ['pdf', 'word', 'excel', 'markdown', 'ppt', 'txt']:
        # 处理常规文件类型
        loader_class, loader_args = LOADER_MAPPING[fileType]  # 获取对应的加载器
        
        # 下载文件到临时位置
        with NamedTemporaryFile(delete=False) as f:
            f.write(requests.get(fileUrl).content)      # 下载文件内容
            f.close()
            
            loader = loader_class(f.name, **loader_args)
            docs = loader.load()                        # 加载文档
            os.unlink(f.name)                          # 删除临时文件
            
            # 合并多个文档片段为单个文档
            merged_doc = Document(
                page_content='\n'.join([d.page_content for d in docs]),
                metadata=docs[0].metadata
            )
            document_id = embedding_single_document(
                merged_doc, fileUrl, fileType, fileName, collection_id, 
                openai=openai, uniqid=uniqid
            )
            document_ids.append(document_id)

    return document_ids
```

### embed_feishuwiki任务 - 飞书知识库同步 (105-156行)
```python
@celery.task()
def embed_feishuwiki(collection_id, openai=False):
    """
    同步飞书知识库的任务
    
    流程:
    1. 获取当前知识库中的文档列表
    2. 获取飞书wiki中的节点列表
    3. 对比差异，处理新增和删除的文档
    """
    # 获取知识库和用户信息
    collection = get_collection_by_id(None, collection_id)
    user = get_user(collection.user_id)
    extra = user.extra.to_dict()
    client = extra.get('client', {})
    
    # 获取飞书wiki中的所有节点
    loader = LarkWikiLoader(collection.space_id, **client)
    nodes = loader.get_nodes()
    current_document_ids = set([node['obj_token'] for node in loader.get_nodes()])
    logging.info("debug current_document_ids %r", current_document_ids)
    
    # 查询已存在的文档
    response = Search(index="document").filter(
        "term", type="feishudoc"                        # 飞书文档类型
    ).filter(
        "term", status=0,                               # 正常状态
    ).filter(
        "term", collection_id=collection_id,            # 指定知识库
    ).extra(
        from_=0, size=10000                             # 获取最多10000条
    ).sort({"modified": {"order": "desc"}}).execute()
    
    exists_document_ids = set([doc.uniqid for doc in response])
    logging.info("debug exists_document_ids %r", exists_document_ids)
    
    # 计算差异
    new_document_ids = current_document_ids - exists_document_ids      # 新增的文档
    deleted_document_ids = exists_document_ids - current_document_ids  # 删除的文档
    
    # 处理重复文档（清理）
    for uniqid in exists_document_ids:
        try:
            # 检查是否有重复的文档ID
            document_ids = [doc.meta.id for doc in response if doc.uniqid == uniqid]
            if len(document_ids) > 1:
                # 保留最新的，删除其他重复的
                for document_id in document_ids[:-1]:
                    purge_document_by_id(document_id)
        except Exception as e:
            logging.error(e)
    
    # 删除已移除的文档
    for document_id in deleted_document_ids:
        try:
            document_ids = get_document_id_by_uniqid(collection_id, document_id)
            for document_id in document_ids:
                purge_document_by_id(document_id)
        except Exception as e:
            logging.error(e)

    # 添加新文档
    for document_id in new_document_ids:
        task = embed_documents.delay(
            f'https://feishu.cn/docx/{document_id}',    # 飞书文档URL
            'feishudoc',                                # 文档类型
            '', collection_id, False, uniqid=document_id
        )
        logging.info("debug add new document %r %r", document_id, task)
    
    return list(new_document_ids), list(exists_document_ids)
```

## 技术特点

### 异步任务处理
- **Celery集成**: 基于Celery的分布式任务队列
- **后台处理**: 文档处理不阻塞主线程
- **任务状态**: 支持任务状态查询和监控

### 多平台文档支持
- **飞书文档**: 支持飞书在线文档和知识库
- **语雀文档**: 支持语雀知识库集成
- **Notion文档**: 支持Notion页面导入
- **常规文件**: 支持PDF、Word、Excel等格式

### 向量化处理
- **文档分块**: 将长文档分割为适合的块
- **向量嵌入**: 使用OpenAI或其他模型生成向量
- **存储索引**: 将向量存储到Elasticsearch

### 增量同步
- **差异检测**: 对比本地和远程文档差异
- **增量更新**: 只处理新增和变更的文档
- **重复清理**: 自动清理重复的文档记录

## 使用场景
- **知识库构建**: 将各种文档导入知识库
- **内容同步**: 定期同步外部平台的内容
- **文档处理**: 批量处理和向量化文档
- **搜索优化**: 为智能搜索提供向量数据

## 错误处理
- **异常捕获**: 完善的异常处理机制
- **重试机制**: 失败任务的自动重试
- **日志记录**: 详细的操作日志记录
- **状态跟踪**: 任务执行状态的跟踪
