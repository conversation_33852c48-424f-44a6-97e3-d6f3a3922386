# 代码文档 - manager-server 项目概览

## 项目作用
企联AI管理服务器后端，基于Tornado框架的异步Web服务，提供用户管理、应用管理、消息处理等核心功能。

## 技术架构
- **Web框架**: Tornado (异步非阻塞)
- **数据库**: MySQL + Redis
- **消息队列**: RabbitMQ
- **部署**: Docker容器化

## 目录结构

### 核心服务
- **server/server.py**: 主服务入口
- **server/api.py**: 路由模块加载器
- **server/admin.py**: 管理后台服务

### 配置管理
- **server/settings/**: 配置文件目录
  - config.py: 主配置文件
  - constant.py: 常量定义

### 核心模块
- **server/core/**: 核心功能模块
  - base_handler.py: 请求处理基类
  - base_model.py: 数据模型基类
  - mysql.py: 数据库连接
  - redisdb.py: Redis客户端
  - rabbitmq.py: 消息队列

### 业务模块
- **server/modules/**: 业务功能模块
  - account/: 账户管理
  - application/: 应用管理
  - callback/: 回调处理
  - messenger/: 消息服务

### 第三方集成
- **server/core/**: 第三方服务集成
  - feishu.py: 飞书集成
  - dingding.py: 钉钉集成
  - wework.py: 企业微信集成
  - openai相关: AI模型集成

### 后台任务
- **server/scripts/**: 后台消费者脚本
  - application_consumer.py: 应用消息消费
  - feishu_consumer.py: 飞书消息消费
  - dingding_consumer.py: 钉钉消息消费

### 部署配置
- **docker/**: Docker部署文件
- **Makefile**: 构建脚本
- **docker-compose.yml**: 容器编排

## 核心功能
- 用户认证和会话管理
- 多平台消息机器人管理
- AI模型集成和调用
- 文件上传和处理
- 实时消息推送
- 多租户支持

## 技术特点
- 异步高并发处理
- 模块化架构设计
- 支持多种AI模型
- 完整的消息队列机制
- 容器化部署支持
