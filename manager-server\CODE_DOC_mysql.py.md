# 代码文档 - server/core/mysql.py

## 文件作用
MySQL数据库连接管理模块，提供数据库引擎创建、连接池管理和Session工厂功能。

## 逐行代码解释

### 导入模块 (1-5行)
```python
import random                    # 随机数生成，用于负载均衡
from tornado.options import options  # Tornado配置选项
from sqlalchemy import create_engine  # SQLAlchemy引擎创建
from sqlalchemy.orm import sessionmaker  # Session工厂
```

### MySQL连接配置 (7-12行)
```python
mysql_options = {
    'pool_size': 64,           # 连接池大小：64个连接
    'pool_recycle': 3599,      # 连接回收时间：3599秒（约1小时）
    'echo': 0 and options.DEBUG,  # SQL日志输出：仅在DEBUG模式下启用
    'max_overflow': 0,         # 最大溢出连接数：0（不允许超出pool_size）
}
```
**说明**:
- `pool_size=64`: 连接池维护64个数据库连接
- `pool_recycle=3599`: 防止MySQL连接超时，定期回收连接
- `echo=0 and options.DEBUG`: 只在调试模式下打印SQL语句
- `max_overflow=0`: 严格限制连接数，不允许临时创建额外连接

### 引擎缓存 (14行)
```python
engines = {}  # 全局引擎缓存字典
```

### get_engine_by_name函数 (17-27行)
```python
def get_engine_by_name(name, isolation_level='REPEATABLE_READ'):
    """根据配置名称获取数据库引擎"""
    
    # 从配置中获取数据库URI，默认使用master配置
    uris = options.MYSQL.get(name, options.MYSQL["master"])
    
    # 确保uris是列表格式（支持多个数据库实例）
    if not isinstance(uris, list):
        uris = [uris]
    
    # 生成缓存键：配置名称+隔离级别
    key = '{}:{}'.format(name, isolation_level)
    
    # 检查引擎是否已缓存
    if key not in engines:
        # 为每个URI创建数据库引擎
        engines[key] = [create_engine(
            uri, isolation_level=isolation_level, **mysql_options
        ) for uri in uris]
    
    # 随机选择一个引擎（负载均衡）
    return random.choice(engines.get(key))
```
**说明**:
- 支持多数据库实例的负载均衡
- 引擎缓存避免重复创建
- 可配置事务隔离级别

### get_session_by_name函数 (30-48行)
```python
def get_session_by_name(name, transaction=False, autocommit=True, **kwargs):
    """
    直接通过数据库配置名称返回对应的Session。
    transaction代表拿到的session是否支持事务
    
    1. transaction=True代表的意思是connection上面`set autocommit=0`。
       这个时候可以使用事务，需要手动成对调用session.begin()和session.commit()。
       主要用在有的业务一次在一个或者多个表插入多条数据的情况。
       除了保持数据一致性方面的考虑。还有就是插入性能会好很多。
       
    2. transaction=False代表connection上面`set autocommit=1`(这个应该是mysql默认的)。
       这个时候也能使用插入或者更新操作（会立即生效）
       这个模式下面polardb能很好的根据sql类型做负载均衡
    """
    return sessionmaker(
        bind=get_engine_by_name(
            name,
            # 根据transaction参数选择隔离级别
            isolation_level='REPEATABLE READ' if transaction else 'AUTOCOMMIT',
        ),
        **kwargs,  # 传递额外的sessionmaker参数
    )
```
**说明**:
- `transaction=True`: 支持手动事务管理，适合批量操作
- `transaction=False`: 自动提交模式，适合单条操作
- 根据事务需求自动选择合适的隔离级别

### 默认Session配置 (51-52行)
```python
# TODO 根据自己需要在这个地方配置不同的Session
Session = get_session_by_name('master', transaction=True, autocommit=True)
```
**说明**:
- 创建默认的Session工厂
- 使用master数据库配置
- 启用事务支持和自动提交

## 技术特点

### 连接池管理
- **固定大小**: 64个连接的固定连接池
- **连接回收**: 定期回收长时间空闲的连接
- **无溢出**: 严格控制连接数量

### 负载均衡
- **随机选择**: 多数据库实例间的随机负载均衡
- **引擎缓存**: 避免重复创建数据库引擎
- **配置灵活**: 支持不同的数据库配置

### 事务管理
- **双模式**: 支持事务模式和自动提交模式
- **隔离级别**: 根据使用场景自动选择隔离级别
- **性能优化**: 批量操作使用事务模式提升性能

### 配置驱动
- **动态配置**: 从options.MYSQL读取数据库配置
- **多实例支持**: 单个配置名可对应多个数据库实例
- **扩展性**: 易于添加新的数据库配置

## 使用场景
- **Web应用**: 为Tornado应用提供数据库连接
- **批量处理**: 事务模式支持大批量数据操作
- **高并发**: 连接池支持高并发访问
- **读写分离**: 可配置不同的读写数据库实例
