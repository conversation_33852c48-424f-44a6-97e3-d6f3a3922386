# 代码文档 - server/core/rabbitmq.py

## 文件作用
RabbitMQ消息队列客户端封装，提供单例模式的消息发送和接收功能，支持自动重连和错误处理。

## 逐行代码解释

### 导入模块 (1-7行)
```python
import pika                              # RabbitMQ Python客户端
import json                              # JSON数据处理
from pika.exceptions import ChannelClosed, ConnectionClosed, UnroutableError  # Pika异常类
import logging                           # 日志记录
from tornado.options import options      # Tornado配置选项
```

### Singleton单例元类 (9-24行)
```python
class Singleton(type):
    """
    单例模式元类
    
    设计原因：
    系统中很多地方直接使用以下代码发送消息：
    pikachu = MqSession()
    pikachu.put(queue, json.dumps({ ... }))
    pikachu.close()
    
    每次创建连接和关闭连接，在消息比较多的时候造成很大的内存和性能开销。
    使用单例模式，只会有一个连接，在put消息时会自动重连，并且重试一次推送消息。
    """
    _instances = {}  # 存储单例实例的字典

    def __call__(cls, *args, **kwargs):
        """控制类的实例化过程"""
        if cls not in cls._instances:
            # 如果该类还没有实例，创建新实例
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]  # 返回已存在的实例
```

### MqSession消息队列会话类 (26-49行)
```python
class MqSession(object, metaclass=Singleton):
    """RabbitMQ会话类 - 使用单例模式"""

    def __init__(self):
        """初始化时建立连接"""
        self.connect()

    def connect(self):
        """建立RabbitMQ连接"""
        try:
            # 创建认证凭据
            self.credentials = pika.PlainCredentials(
                username=options.RABBIT_MQ_USER,      # RabbitMQ用户名
                password=options.RABBIT_MQ_PASSWORD,  # RabbitMQ密码
            )
            
            # 创建阻塞连接
            self.connection = pika.BlockingConnection(pika.ConnectionParameters(
                host=options.RABBIT_MQ_SERVER,        # RabbitMQ服务器地址
                credentials=self.credentials,         # 认证凭据
                blocked_connection_timeout=2,         # 2秒超时，避免阻塞
                port=options.RABBIT_MQ_PORT,          # RabbitMQ端口
            ))
            
            # 创建通道
            self.channel = self.connection.channel()
            self.channel.basic_qos(prefetch_count=1)  # 设置预取数量为1
            self.channel.confirm_delivery()           # 启用发送确认机制
            
        except Exception as e:
            logging.exception(e)
            return
```

### _put私有发送方法 (51-61行)
```python
def _put(self, queue, body, priority=0, expiration=None):
    """私有方法：实际执行消息发送"""
    self.channel.basic_publish(
        exchange=options.RABBIT_MQ_EXCHANGE,  # 交换机名称
        routing_key=queue,                    # 路由键（队列名）
        body=body.encode('utf-8'),            # 消息体（UTF-8编码）
        properties=pika.BasicProperties(
            delivery_mode=2,                  # 2=消息持久化
            priority=priority,                # 消息优先级
            expiration=expiration and str(expiration) or None,  # 过期时间
        ),
    )
```

### put公共发送方法 (63-77行)
```python
def put(self, queue, body, priority=0, expiration=None):
    """公共方法：发送消息，支持自动重连"""
    try:
        self._put(queue, body, priority, expiration)  # 尝试发送消息
    except (ConnectionClosed, ChannelClosed, UnroutableError) as e:
        # 捕获连接相关异常，进行重连和重试
        logging.warning("reconnect and push msg: %r to queue: %r %r", body, queue, e)
        try:
            self.connection.close()  # 关闭旧连接
        except Exception as e:
            logging.warn("exception for close connection %r", e)
        
        self.connect()  # 重新建立连接
        self._put(queue, body, priority, expiration)  # 重试发送消息
        
    except Exception as e:
        # 其他异常，记录错误并返回失败标识
        logging.exception(e)
        logging.warning("push msg: %r to queue: %r failed", body, queue)
        return -1
```

### get消息接收方法 (79-85行)
```python
def get(self, queue):
    """从队列接收消息"""
    try:
        # 消费队列中的消息
        for method_frame, properties, body in self.channel.consume(queue):
            return method_frame.delivery_tag, body.decode('utf-8')  # 返回投递标签和消息体
    except Exception as e:
        logging.exception(e)
        return None
```

### ack消息确认方法 (87-92行)
```python
def ack(self, delivery_tag):
    """确认消息已处理"""
    try:
        self.channel.basic_ack(delivery_tag)  # 发送ACK确认
    except Exception as e:
        logging.exception(e)
        return -1
```

### close关闭方法 (94-99行)
```python
def close(self):
    """关闭连接（单例模式下为空实现）"""
    # 使用单例模式之后，不用每次都创建连接，所以就不用真实的关闭连接了
    # 在程序退出的时候自动关闭连接
    # self.connection.close()
    pass
```

## 技术特点

### 单例模式设计
- **性能优化**: 避免频繁创建和销毁连接，提升性能
- **资源节约**: 整个应用只维护一个RabbitMQ连接
- **线程安全**: 元类实现的单例模式确保线程安全

### 自动重连机制
- **异常捕获**: 捕获连接断开、通道关闭等异常
- **自动重连**: 异常时自动重新建立连接
- **重试机制**: 重连后自动重试发送消息

### 消息可靠性
- **消息持久化**: delivery_mode=2确保消息持久化存储
- **发送确认**: confirm_delivery()启用发送确认机制
- **消息确认**: 支持消费者手动确认消息处理完成

### 配置化管理
- **外部配置**: 所有连接参数通过options配置
- **灵活部署**: 支持不同环境的配置切换
- **超时控制**: 设置连接超时避免长时间阻塞

## 使用场景

### 异步任务处理
- **后台任务**: 将耗时任务放入队列异步处理
- **消息通知**: 发送各种类型的通知消息
- **数据同步**: 不同服务间的数据同步

### 系统解耦
- **服务解耦**: 通过消息队列解耦不同服务
- **流量削峰**: 通过队列缓冲高并发请求
- **可靠传输**: 确保消息的可靠传递

## 使用示例
```python
# 发送消息
mq = MqSession()
mq.put('queue-name', json.dumps({'key': 'value'}))

# 接收消息
delivery_tag, message = mq.get('queue-name')
if message:
    # 处理消息
    data = json.loads(message)
    # 确认消息
    mq.ack(delivery_tag)
```

## 注意事项
- **单例特性**: 整个应用共享一个连接实例
- **异常处理**: 需要处理网络异常和连接断开
- **消息格式**: 消息体需要是字符串格式，通常使用JSON
- **队列管理**: 需要预先创建队列和交换机
