# 代码文档 - server/core/redisdb.py

## 文件作用
Redis数据库客户端和装饰器工具模块，提供分布式锁、缓存、限流等功能。

## 逐行代码解释

### 导入模块 (1-13行)
```python
import asyncio                    # 异步编程支持
import random                     # 随机数生成
import functools                  # 函数装饰器工具
import logging                    # 日志记录
import pickle                     # 对象序列化
from inspect import iscoroutinefunction  # 检查是否为协程函数

import redis                      # Redis客户端
from binascii import crc32        # CRC32哈希算法
from tornado.ioloop import IOLoop  # Tornado事件循环
from tornado.options import options  # Tornado配置选项
from .exception import PermissionDenied  # 权限拒绝异常
```

### Redis连接池配置 (15-29行)
```python
# 自动解码响应的连接池
decode_redis_pool = redis.ConnectionPool(
    socket_connect_timeout=0.5,   # 连接超时：0.5秒
    host=options.REDIS_HOST,      # Redis主机地址
    port=options.REDIS_PORT,      # Redis端口
    db=options.REDIS_DB,          # Redis数据库编号
    decode_responses=True,        # 自动解码响应为字符串
)

# 不自动解码的连接池（用于二进制数据）
redis_pool = redis.ConnectionPool(
    socket_connect_timeout=0.5,
    host=options.REDIS_HOST,
    port=options.REDIS_PORT,
    db=options.REDIS_DB,
    decode_responses=False,       # 保持二进制格式
)
```

### Redis客户端工厂函数 (32-35行)
```python
def redis_cli(decode_responses=True):
    """创建Redis客户端实例"""
    return redis.StrictRedis(
        connection_pool=decode_redis_pool if decode_responses else redis_pool,
    )
```

### 工具函数 (38-48行)
```python
def gen_prefix(obj, method):
    """生成方法的唯一前缀：模块名.类名.方法名"""
    return '.'.join([obj.__module__, obj.__class__.__name__, method.__name__])

def get_name(self, method, args, kwargs, key=None, prefix=None, attr_key=None, attr_prefix=None, namespace=options.REDIS_NAMESPACE):
    """生成Redis键名"""
    # 优先使用指定的key或从kwargs中获取key
    name = key or kwargs.get('key', None) or (attr_key and getattr(self, attr_key))
    
    if not name:
        # 生成前缀：指定前缀 > 对象属性前缀 > 自动生成前缀
        _prefix = prefix or (attr_prefix and getattr(self, attr_prefix)) or gen_prefix(self, method)
        # 使用CRC32哈希参数生成唯一键名
        name = "%s:%u" % (_prefix, crc32(pickle.dumps(args) + pickle.dumps(kwargs)))
    
    # 添加命名空间前缀
    name = namespace and "{}:{}".format(namespace, name) or name
    return name
```

### 分布式锁装饰器 (51-96行)
```python
def lock(key=None, prefix=None, attr_key=None, attr_prefix=None, timeout=60, namespace=options.REDIS_NAMESPACE, retry=1, retry_delay=1e-1):
    """分布式锁装饰器"""
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            """同步方法包装器"""
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            redis_lock = redis.lock.Lock(redis_cli(), name, timeout=timeout)
            
            if redis_lock.acquire(blocking=False):  # 非阻塞获取锁
                try:
                    res = method(self, *args, **kwargs)  # 执行原方法
                    return res
                finally:
                    try:
                        redis_lock.release()  # 释放锁
                    except Exception as e:
                        logging.exception(e)
            else:
                logging.info("lock name: %r, (*%r, **%r)", name, args, kwargs)
                raise PermissionDenied('任务忙，请稍后再试')

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            """异步方法包装器"""
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            retry_time = retry if retry > 0 else 1
            
            while retry_time > 0:  # 重试机制
                retry_time = retry_time - 1
                redis_lock = redis.lock.Lock(redis_cli(), name, timeout=timeout)
                
                if redis_lock.acquire(blocking=False):
                    try:
                        res = await method(self, *args, **kwargs)
                        return res
                    finally:
                        try:
                            redis_lock.release()
                        except Exception as e:
                            logging.exception(e)
                else:
                    if retry_time <= 0 or retry_delay <= 0:
                        logging.debug("lock name: %r, (*%r, **%r)", name, args, kwargs)
                        raise PermissionDenied('任务忙，请稍后再试')
                    
                    # 异步延时重试
                    logging.debug("retry: %r to get lock for: %r in %rs", retry, name, retry_delay)
                    await asyncio.sleep(retry_delay)

        return async_wrapper if iscoroutinefunction(method) else wrapper
    return decorate
```

### 过期缓存装饰器 (99-160行)
```python
def stalecache(key=None, prefix=None, attr_key=None, attr_prefix=None, namespace=options.REDIS_NAMESPACE,
               expire=600, stale=3600, time_lock=1, time_delay=1, max_time_delay=10):
    """过期缓存装饰器 - 支持返回过期数据并异步更新"""
    def decorate(method):
        @functools.wraps(method)
        def wrapper(self, *args, **kwargs):
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            
            # 获取TTL和缓存值
            res = redis_cli(False).pipeline().ttl(name).get(name).execute()
            v = pickle.loads(res[1]) if res[0] > 0 and res[1] else None
            
            # 检查是否需要更新缓存
            if res[0] <= 0 or res[0] < stale:
                def func():
                    """缓存更新函数"""
                    value = method(self, *args, **kwargs)
                    logging.debug("update cache: %s", name)
                    redis_cli(False).pipeline().set(
                        name, pickle.dumps(value)
                    ).expire(name, expire + stale).execute()
                    return value

                # 缓存不存在时阻塞更新
                if res[0] <= 0:
                    return func()

                # 缓存过期时非阻塞更新，返回过期数据
                real_time_delay = random.randrange(time_delay, max_time_delay)
                redis_cli(False).expire(name, stale + real_time_delay + time_lock)
                IOLoop.current().add_timeout(IOLoop.current().time() + real_time_delay, func)

            return v

        @functools.wraps(method)
        async def async_wrapper(self, *args, **kwargs):
            """异步版本的缓存装饰器"""
            if kwargs.get('skip_cache'):  # 跳过缓存选项
                return await method(self, *args, **kwargs)

            # 实现逻辑与同步版本相同
            name = get_name(self, method, args, kwargs, key=key, prefix=prefix, attr_key=attr_key, attr_prefix=attr_prefix, namespace=namespace)
            res = redis_cli(False).pipeline().ttl(name).get(name).execute()
            v = pickle.loads(res[1]) if res[0] > 0 and res[1] else None
            
            if res[0] <= 0 or res[0] < stale:
                async def func():
                    value = await method(self, *args, **kwargs)
                    logging.debug("update cache: %s", name)
                    redis_cli(False).pipeline().set(
                        name, pickle.dumps(value)
                    ).expire(name, expire + stale).execute()
                    return value

                if res[0] <= 0:
                    return await func()

                real_time_delay = random.randrange(time_delay, max_time_delay)
                redis_cli(False).expire(name, stale + real_time_delay + time_lock)
                IOLoop.current().add_timeout(IOLoop.current().time() + real_time_delay, func)

            return v

        return async_wrapper if iscoroutinefunction(method) else wrapper
    return decorate
```

## 技术特点
- **分布式锁**: 基于Redis的分布式锁机制
- **智能缓存**: 支持过期数据返回和异步更新
- **限流控制**: 基于令牌桶的API限流
- **异步支持**: 同时支持同步和异步方法
- **灵活配置**: 支持多种键名生成策略
