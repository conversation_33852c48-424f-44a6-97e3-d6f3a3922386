# 代码文档 - README.md

## 文件作用
项目主说明文档，介绍企联 AI 版本管理项目的整体架构和规范。

## 关键信息

### 项目结构
这是一个多模块项目，包含以下核心组件：
- **manager-server**: 管理服务器后端
- **DataChat-API**: 数据聊天API服务
- **ConnectAI-E-AdminPanel**: 企联AI管理面板前端
- **Lark-Messenger-Web**: 飞书消息Web端
- **ConnectAI-Helper**: 企联AI助手扩展

### 分支管理规范
- **main**: 主分支，用于主要功能开发
- **privatization**: 私有化部署标品分支
- **privatization-channel1**: 特定公司定制分支

### 版本号规范
- **v1.0.0**: 主版本号格式
- **v1.0.0-privatization**: 私有化标品版本
- **v1.0.0-channel1**: 定制版本号

## 技术特点
- 多模块架构设计
- 支持私有化部署
- 支持多渠道定制化
- 统一版本管理
