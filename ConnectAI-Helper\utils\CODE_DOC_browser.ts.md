# 代码文档 - utils/browser.ts

## 文件作用
浏览器扩展的工具函数库，提供窗口管理、飞书登录认证、CSRF令牌获取等浏览器相关功能。

## 逐行代码解释

### openCenteredWindow函数 - 居中窗口 (2-12行)
```typescript
export function openCenteredWindow(url, width = 800, height = 600) {
  // 计算居中显示的窗口位置
  const left = (window.screen.width - width) / 2;    // 水平居中位置
  const top = (window.screen.height - height) / 2;   // 垂直居中位置

  // 窗口特性字符串
  const features = `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`;

  // 打开新窗口
  window.open(url, '_blank', features);
}
```
**说明**:
- 在屏幕中央打开指定大小的新窗口
- 默认尺寸: 800x600像素
- 支持窗口缩放和滚动条

### openCenteredPopup函数 - 居中弹窗 (14-25行)
```typescript
export function openCenteredPopup(url='https://open.feishu.cn/app') {
  var screenWidth = window.innerWidth;               // 获取当前窗口宽度
  var screenHeight = window.innerHeight;             // 获取当前窗口高度
  var popupWidth = Math.round(screenWidth * 5 / 6); // 弹窗宽度为屏幕的5/6
  var popupHeight = Math.round(screenHeight * 5 / 6); // 弹窗高度为屏幕的5/6
  var left = (screenWidth - popupWidth) / 2;         // 计算水平居中位置
  var top = (screenHeight - popupHeight) / 2;        // 计算垂直居中位置

  // 打开一个居中的新弹出窗口
  var popupWindow = window.open(url, 'popupWindow', `width=${popupWidth},height=${popupHeight},left=${left},top=${top}`);
  return popupWindow;                                // 返回窗口对象
}
```
**说明**:
- 默认打开飞书应用页面
- 弹窗大小为当前窗口的5/6
- 返回窗口对象供后续操作

### 正则表达式定义 (28-31行)
```typescript
const csrfTokenRegex = /window.csrfToken="([^"]+)";/;      // 匹配CSRF令牌
const displayNameRegex = /"displayName":{"value":"([^"]+)"/; // 匹配用户显示名
const avatarRegex = /"avatar":"([^"]+)"/;                   // 匹配用户头像
const tenantRegex = /"tenantName":"([^"]+)"/;               // 匹配租户名称
```
**说明**:
- 用于从飞书页面HTML中提取用户信息
- 通过正则表达式解析JavaScript变量和JSON数据

### 域名常量 (33-34行)
```typescript
export const FEISHU_DOMAIN = 'open.feishu.cn'        // 飞书国内版域名
export const LARK_DOMAIN = 'open.larksuite.com'      // Lark国际版域名
```

### getCsrfToken函数 - 获取CSRF令牌 (36-52行)
```typescript
export function getCsrfToken(domain=FEISHU_DOMAIN) {
  return fetch(`https://${domain}/app`, {
    mode: "cors",                                     // 跨域模式
    credentials: "include",                           // 包含Cookie
    referrerPolicy: "no-referrer"                     // 不发送Referrer
  }).then(res => res.text()).then(content => {
    // 使用正则表达式提取页面中的用户信息
    const csrfTokenMatch = content.match(csrfTokenRegex);
    const displayNameMatch = content.match(displayNameRegex);
    const avatarMatch = content.match(avatarRegex);
    const tenantMatch = content.match(tenantRegex);
    
    if (csrfTokenMatch && displayNameMatch && avatarMatch && tenantMatch) {
      return {
        csrf_token: csrfTokenMatch[1],                // CSRF令牌
        display_name: displayNameMatch[1],            // 用户显示名
        avatar: avatarMatch[1],                       // 用户头像URL
        tenant: tenantMatch[1],                       // 租户名称
      }
    }
    return Promise.reject()                           // 解析失败时拒绝Promise
  })
}
```
**说明**:
- 通过访问飞书应用页面获取用户认证信息
- 解析页面HTML提取CSRF令牌和用户信息
- 返回包含认证信息的对象

### feishuLogin函数 - 飞书登录 (54-76行)
```typescript
export function feishuLogin(domain=FEISHU_DOMAIN) {
  return new Promise((resolve) => {
    // 创建弹窗用于用户登录
    chrome.windows.create({
      url: `https://${domain}/app`,
      type: 'popup',                                  // 弹窗类型
      width: 400,                                     // 弹窗宽度
      height: 600,                                    // 弹窗高度
      left: 400,                                      // 弹窗位置
      top: 200,
    }, function(w){
      // 定时检查登录状态
      const i = setInterval(() => {
        getCsrfToken(domain).then(csrfToken => {
          clearInterval(i)                            // 清除定时器
          console.log('remove window', w.id)
          // 登录成功后关闭弹窗
          chrome.windows.remove(w.id, function(res) {
            console.log('remove window', res)
          })
          resolve(csrfToken)                          // 返回认证信息
        }).catch(e => console.error(e))
      }, 500)                                         // 每500ms检查一次
    })
  })
}
```
**说明**:
- 打开飞书登录弹窗
- 定时检查用户是否完成登录
- 登录成功后自动关闭弹窗并返回认证信息

### getCookieAndToken函数 - 获取认证信息 (78-83行)
```typescript
export function getCookieAndToken(domain=FEISHU_DOMAIN) {
  return getCsrfToken(domain).catch(e => {
    // 没有登录，就打开页面扫码登录，并且重新获取csrfToken
    return feishuLogin(domain)
  })
}
```
**说明**:
- 尝试获取已有的认证信息
- 如果未登录，自动触发登录流程
- 提供统一的认证信息获取接口

## 技术特点

### 窗口管理
- **居中显示**: 自动计算窗口居中位置
- **响应式大小**: 根据屏幕大小调整弹窗尺寸
- **窗口控制**: 支持窗口的创建和关闭

### 认证机制
- **CSRF保护**: 获取和使用CSRF令牌
- **Cookie管理**: 自动处理登录状态的Cookie
- **自动登录**: 检测到未登录时自动触发登录流程

### 跨域处理
- **CORS配置**: 正确配置跨域请求
- **凭证传递**: 包含必要的认证凭证
- **安全策略**: 合理的Referrer策略

### 多平台支持
- **飞书/Lark**: 支持国内外两个版本
- **域名切换**: 通过参数切换不同域名
- **统一接口**: 提供统一的API接口

## 使用场景

### 用户认证
- **自动登录**: 检测登录状态并自动登录
- **令牌获取**: 获取API调用所需的CSRF令牌
- **用户信息**: 提取用户的基本信息

### 窗口操作
- **弹窗显示**: 在合适位置显示弹窗
- **页面跳转**: 打开外部页面或应用
- **用户交互**: 提供良好的用户交互体验

### 扩展集成
- **Chrome API**: 使用Chrome扩展API
- **权限管理**: 处理扩展权限和安全
- **状态同步**: 同步登录状态和用户信息

## 安全考虑
- **CSRF防护**: 正确处理CSRF令牌
- **同源策略**: 遵循浏览器安全策略
- **凭证安全**: 安全地处理用户凭证
- **权限最小化**: 只请求必要的权限

## 错误处理
- **网络异常**: 处理网络请求失败
- **解析错误**: 处理页面解析失败
- **登录失败**: 处理登录流程异常
- **窗口操作**: 处理窗口操作失败
