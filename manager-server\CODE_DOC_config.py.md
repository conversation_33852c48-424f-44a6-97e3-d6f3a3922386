# 代码文档 - server/settings/config.py

## 文件作用
系统配置管理中心，定义所有服务的配置参数和默认值。

## 配置分类

### 服务器基础配置
- **DEBUG**: 调试模式 (默认 True)
- **ENV**: 环境标识 (默认 test)
- **SERVER_PORT**: 服务端口 (默认 3000)
- **SCHEMA/DOMAIN**: 服务域名配置
- **KNOW_SERVER**: 知识服务器地址
- **PROXY_BASE_SERVER**: 代理服务器配置

### 数据库配置
- **MYSQL**: 主数据库连接配置
  - 连接字符串: `mysql+pymysql://root:connectai2023@mysql:3306/connectai-manager`
- **REDIS**: 缓存数据库配置
  - 主机: redis, 端口: 6379, 命名空间: connectai

### 消息队列配置
- **RabbitMQ**: 异步任务队列
  - 服务器: rabbitmq:5672
  - 交换机: connectai
  - 队列配置:
    - queue-application: 应用队列
    - queue-feishu: 飞书队列
    - queue-dingding: 钉钉队列
    - queue-wework: 企业微信队列
    - queue-messenger: 消息队列

### 第三方服务配置
- **腾讯云服务**: SMS短信、支付服务
- **微信支付**: 商户配置、证书配置
- **企业微信**: 企业ID、Token、密钥配置
- **ChatPDF**: 文档处理服务配置

### 存储配置
- **OSS**: 对象存储服务
  - CDN地址: `https://pic1.forkway.cn`
  - 上传路径: upload

### 代理配置
- **PROXY_WEB**: Web代理配置
- **PROXY_HOST/PORT**: 代理服务器地址

### 系统机器人配置
- **SYSTEM_BOT**: 系统机器人配置
- **BITABLE**: 多维表格配置

## 配置加载机制
- 使用 Tornado 的 `define` 函数定义配置项
- 支持从外部配置文件 `etc/web_config.conf` 覆盖默认值
- 配置文件路径自动解析

## 技术特点
- 集中化配置管理
- 支持环境变量覆盖
- 模块化配置分组
- 生产环境配置隔离
