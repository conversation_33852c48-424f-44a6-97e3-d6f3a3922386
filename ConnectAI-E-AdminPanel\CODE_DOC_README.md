# 代码文档 - ConnectAI-E-AdminPanel 项目概览

## 项目作用
企联AI管理面板前端，基于Vue3的现代化Web管理界面，提供AI应用管理、用户管理、数据分析等功能。

## 技术架构
- **前端框架**: Vue 3.3 + Composition API
- **构建工具**: Vite 4.3
- **UI组件库**: Naive UI 2.34.3
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: UnoCSS + Tailwind CSS

## 目录结构

### 核心文件
- **src/App.vue**: 应用根组件
- **src/main.ts**: 应用入口
- **index.html**: HTML模板

### 组件系统
- **src/components/**: 通用组件
- **src/layouts/**: 布局组件
- **src/views/**: 页面组件

### 状态管理
- **src/store/**: Pinia状态管理
- **src/composables/**: 组合式函数

### 路由配置
- **src/router/**: 路由配置

### 工具模块
- **src/utils/**: 工具函数
- **src/service/**: API服务
- **src/hooks/**: React风格Hooks

### 配置文件
- **src/config/**: 应用配置
- **src/settings/**: 设置管理
- **src/constants/**: 常量定义

### 国际化
- **src/locales/**: 多语言支持

### 样式资源
- **src/styles/**: 全局样式
- **src/assets/**: 静态资源

### 构建配置
- **build/**: 构建配置
- **vite.config.ts**: Vite配置
- **uno.config.ts**: UnoCSS配置

## 核心功能
- 用户认证和权限管理
- AI应用配置和管理
- 数据可视化和分析
- 多语言国际化支持
- 响应式设计
- 主题切换

## 开发特性
- **热重载**: 开发时实时更新
- **类型安全**: TypeScript支持
- **代码规范**: ESLint + Prettier
- **组件自动导入**: 提升开发效率
- **Mock数据**: 开发阶段数据模拟

## 构建部署
- **多环境支持**: dev/test/prod
- **飞书定制版**: 支持飞书专版构建
- **Vercel部署**: 支持Vercel平台
- **Docker部署**: 容器化部署支持

## 技术特点
- 现代化Vue3生态
- 组件化开发模式
- 响应式状态管理
- 原子化CSS方案
- 完整的开发工具链
