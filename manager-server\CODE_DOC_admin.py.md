# 代码文档 - server/admin.py

## 文件作用
基于Gradio的管理后台界面，提供租户管理、账户注册、套餐升级等管理功能。

## 逐行代码解释

### 导入模块 (1-11行)
```python
import asyncio          # 异步编程支持
import logging          # 日志记录
import json            # JSON数据处理
import pandas as pd    # 数据分析库，用于DataFrame展示
from tornado.options import options  # Tornado配置选项

from settings.config import load_config  # 加载配置文件
load_config()          # 执行配置加载
from modules.account.model import AdminModel  # 管理员数据模型
import gradio as gr    # Gradio Web界面框架
```

### refresh函数 (13-22行)
```python
def refresh():
    """刷新租户列表数据"""
    with AdminModel() as model:  # 使用上下文管理器创建模型实例
        # 获取租户列表：查询条件为空，第1页，每页200条
        tenant, total = model.get_tenant_list('', 1, 200)
        # 将租户数据转换为pandas DataFrame格式
        value = pd.DataFrame(tenant)
        # 提取所有租户的邮箱地址作为下拉选项
        choices = [i["email"] for i in tenant]
        logging.info("refresh %r", choices)  # 记录刷新日志
        # 返回Gradio组件更新字典
        return {
            tenant_list: gr.update(value=value),      # 更新租户列表表格
            tenant_email: gr.update(choices=choices), # 更新邮箱下拉选项
        }
```

### upgrade_by_name函数 (25-29行)
```python
def upgrade_by_name(tenant_email, product_name, product_time, seats):
    """根据邮箱升级租户套餐"""
    logging.info("upgrade_by_name %r", (tenant_email, product_name, product_time, seats))
    with AdminModel() as model:  # 创建管理员模型实例
        # 执行升级操作：邮箱、套餐名称、时长、座席数
        model.upgrade(tenant_email, product_name, product_time, seats)
    return refresh()  # 升级完成后刷新数据
```

### register函数 (32-35行)
```python
def register(email, passwd, tenant_name, api_key):
    """注册新租户账户"""
    with AdminModel() as model:  # 创建管理员模型实例
        # 注册新账户：邮箱、密码、租户名称、API密钥
        model.register(email, passwd, tenant_name=tenant_name, api_key=api_key)
    return refresh()  # 注册完成后刷新数据
```

### Gradio界面定义 (38-74行)
```python
with gr.Blocks(theme="soft") as admin:  # 创建Gradio应用，使用soft主题
    with gr.Tab("帐号"):  # 创建"帐号"标签页
        with gr.Row():    # 创建水平布局行
            with gr.Column(scale=1):  # 左侧列，注册区域
                # 租户名称输入框
                tenant_name = gr.Textbox(label='公司信息(ai-feishu)')
                email = gr.Textbox(label='邮箱')        # 邮箱输入框
                passwd = gr.Textbox(label='密码')       # 密码输入框
                api_key = gr.Textbox(label='API KEY')   # API密钥输入框
                register_button = gr.Button("注册")     # 注册按钮
                
            with gr.Column(scale=1):  # 右侧列，升级区域
                # 租户邮箱下拉选择框
                tenant_email = gr.Dropdown(
                    multiselect=False,  # 单选模式
                    label="选择帐号",
                )
                seats = gr.Number(label="座席", value=10)  # 座席数量，默认10
                # 套餐类型下拉选择
                product_name = gr.Dropdown(
                    multiselect=False,
                    label="选择套餐",
                    choices=['体验版', '个人版', '企业版'],  # 预定义套餐选项
                )
                # 套餐时长下拉选择
                product_time = gr.Dropdown(
                    multiselect=False,
                    label="选择时间",
                    choices=['7 day', '15 day', '1 month', '3 month', '1 year']
                )
                upgrade_button = gr.Button("升级")  # 升级按钮

        refresh_button = gr.Button("刷新")  # 刷新按钮
        # 租户列表数据表格
        tenant_list = gr.DataFrame(
            headers=["id", "name", "email", "resource_count", "app_count", 
                    "bot_count", "product_count", "product_expired", "status", "created"],
        )
```

### 事件绑定 (71-74行)
```python
# 注册按钮点击事件：调用register函数，传入输入参数，更新输出组件
register_button.click(fn=register, inputs=[email, passwd, tenant_name, api_key], 
                     outputs=[tenant_list, tenant_email])

# 升级按钮点击事件：调用upgrade_by_name函数
upgrade_button.click(fn=upgrade_by_name, 
                    inputs=[tenant_email, product_name, product_time, seats], 
                    outputs=[tenant_list, tenant_email])

# 刷新按钮点击事件：调用refresh函数
refresh_button.click(fn=refresh, outputs=[tenant_list, tenant_email])

# 页面加载事件：自动刷新数据
admin.load(refresh, outputs=[tenant_list, tenant_email])
```

### 主程序入口 (77-83行)
```python
if __name__ == "__main__":
    admin.queue().launch(  # 启动Gradio应用
        auth=("connectai", "connectai@2023"),  # HTTP基础认证
        server_name='0.0.0.0',                # 监听所有网络接口
        server_port=options.SERVER_PORT,       # 使用配置的服务器端口
        show_error=True,                       # 显示错误信息
    )
```

## 技术特点
- **Gradio框架**: 快速构建Web管理界面
- **数据表格**: 使用pandas DataFrame展示租户信息
- **实时更新**: 操作完成后自动刷新数据
- **权限控制**: HTTP基础认证保护管理界面
- **响应式布局**: 左右分栏的用户友好界面

## 功能模块
- **租户注册**: 创建新的租户账户
- **套餐升级**: 修改租户的产品套餐和座席数
- **数据展示**: 实时显示租户列表和统计信息
- **操作日志**: 记录所有管理操作
