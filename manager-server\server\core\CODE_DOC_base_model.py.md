# 代码文档 - server/core/base_model.py

## 文件作用
定义数据模型基类，提供MySQL和Redis数据访问的统一接口和上下文管理。

## 逐行代码解释

### 导入模块 (1-17行)
```python
import json                              # JSON数据处理
import asyncio                           # 异步编程支持
import functools                         # 函数装饰器工具
import time                              # 时间处理
from collections import namedtuple       # 命名元组
import nest_asyncio                      # 嵌套异步事件循环支持

from sqlalchemy import func, sql         # SQLAlchemy函数和SQL工具
from sqlalchemy.orm.scoping import scoped_session  # 作用域会话
from tornado.httpclient import AsyncHTTPClient, HTTPRequest  # Tornado HTTP客户端
from tornado.options import options      # Tornado配置选项
from core.mysql import get_session_by_name  # MySQL会话工厂
from core.redisdb import redis_cli       # Redis客户端
from core.utils import row2dict          # 行数据转字典工具
from tornado.concurrent import run_on_executor  # 异步执行器装饰器
from concurrent.futures import ThreadPoolExecutor  # 线程池执行器
```

### 上下文定义 (19-37行)
```python
Context = namedtuple("HandlerContext", "current_user")  # 定义上下文命名元组

class ContextMaker:
    """上下文创建器基类 - model需要的上下文，与RequestHandler解耦"""
    
    def __call__(self, *args, **kwargs):
        return Context(current_user=None)  # 返回空上下文

class HandlerContextMaker(ContextMaker):
    """处理器上下文创建器 - 接收RequestHandler实例，生成model上下文"""
    
    def __call__(self, handler):
        return Context(current_user=handler.current_user)  # 从handler提取用户信息

# 默认的处理器上下文创建器
HandlerContext = HandlerContextMaker()
```

### BaseModel基类 (40-58行)
```python
class BaseModel(object):
    """model基类，约定上下文管理"""

    def __init__(self, *args, context: Context = None, **kwargs):
        self.context = context  # 保存上下文
        if context and context.current_user:
            setattr(self, 'current_user', context.current_user)  # 设置当前用户
        for key, value in kwargs.items():
            setattr(self, key, value)  # 设置额外属性

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exception_type, exception_value, traceback):
        """上下文管理器出口"""
        self.clear()  # 清理资源

    def clear(self):
        """释放资源 - 子类重写此方法"""
        pass
```

### MysqlModel类 (60-146行)
```python
class MysqlModel(BaseModel):
    """用于连接MySQL的model"""

    executor = ThreadPoolExecutor(10)  # 线程池执行器，10个线程

    def __init__(self, *args, engine='master', **kwargs):
        self.engine = engine  # 数据库引擎名称，默认master
        super().__init__(*args, **kwargs)

    def _create_session(self, name=None, transaction=True):
        """创建数据库会话"""
        return scoped_session(
            get_session_by_name(
                name or self.engine,      # 使用指定引擎或默认引擎
                transaction=transaction,   # 是否支持事务
                expire_on_commit=False,   # 提交后不过期对象
                autocommit=True,          # 自动提交
            ),
            scopefunc=lambda: self       # 作用域函数，绑定到当前实例
        )

    @property
    def session(self):
        """主会话属性 - 懒加载"""
        if not hasattr(self, '_session'):
            self._session = self._create_session()
        return self._session

    @property
    def slave_session(self):
        """从库会话属性 - 只用于查询"""
        if not hasattr(self, '_slave_session'):
            self._slave_session = self._create_session(name="slaves", transaction=False)
        return self._slave_session

    @run_on_executor
    def query_one(self, query):
        """查询一条记录"""
        return query.with_session(
            self.slave_session()  # 使用从库查询
        ).first()

    @run_on_executor
    def query_one_page(self, query, page, size):
        """分页查询"""
        if not isinstance(page, int):
            page = int(page)
        if not isinstance(size, int):
            size = int(size)
        if size <= 0:
            return []
        
        offset = (page - 1) * int(size)  # 计算偏移量
        return query.offset(
            offset if offset > 0 else 0
        ).limit(
            # 限制每页最大100条，特殊情况允许1000或99999
            size if (size < 100 or size == 1000 or size == 99999) else 100
        ).with_session(
            self.slave_session()
        ).all()

    @run_on_executor
    def query_all(self, query):
        """查询所有记录"""
        return query.with_session(self.slave_session()).all()

    @run_on_executor
    def query_total(self, query):
        """查询总数 - 处理不同类型的查询"""
        if getattr(query, "_limit", None):
            # 有LIMIT的查询
            return int(query.with_entities(
                sql.literal_column('1')
            ).with_session(self.slave_session()).count() or 0)
        
        if getattr(query, "_group_by", None):
            # 有GROUP BY的查询
            return int(query.with_entities(
                sql.literal_column('1')
            ).order_by(None).with_session(self.slave_session()).count() or 0)
        
        # 普通查询，使用COUNT优化
        return int(self.slave_session.execute(
            query.with_labels().statement.with_only_columns(
                func.count(1)
            ).order_by(None)
        ).scalar() or 0)

    def clear(self):
        """释放数据库连接"""
        if hasattr(self, '_session'):
            self._session.remove()
        if hasattr(self, '_slave_session'):
            self._slave_session.remove()
        super().clear()

    def copy_obj(self, obj, extra=dict(), autocommit=True):
        """复制对象"""
        doc = {k: v for k, v in obj.__dict__.items() if not k.startswith('_')}
        doc.update(extra)  # 添加额外属性
        newobj = obj.__class__(**doc)  # 创建新对象
        self.session.add(newobj)
        if autocommit:
            self.session.commit()
        return newobj.id

    def format_obj(self, obj, **kwargs):
        """格式化对象为字典"""
        res = row2dict(obj)  # 转换为字典
        res.update(kwargs)   # 添加额外字段
        return res
```

### RedisModel类 (163-196行)
```python
class RedisModel(BaseModel):
    """用于连接Redis的model"""

    def __init__(self, *args, **kwargs):
        self.redis_client = redis_cli()  # 创建Redis客户端
        super().__init__(*args, **kwargs)

    @run_on_executor
    def listen(self, channel, timeout):
        """同步监听Redis队列"""
        now = time.time()
        timeout = now + timeout
        while now < timeout:
            message = self.redis_client.lpop(channel)  # 从队列左侧弹出消息
            if message is not None:
                return message
            time.sleep(0.1)  # 短暂休眠
            now = time.time()

    async def alisten(self, channel, timeout):
        """异步监听Redis队列"""
        now = time.time()
        timeout = now + timeout
        while now < timeout:
            message = self.redis_client.lpop(channel)
            if message is not None:
                return message
            await asyncio.sleep(0.1)  # 异步休眠
            now = time.time()

    def send(self, channel, *message, expire=100):
        """发送消息到Redis队列"""
        self.redis_client.expire(channel, expire)  # 设置队列过期时间
        return self.redis_client.rpush(channel, *message)  # 从右侧推入消息
```

### 异步支持 (198行)
```python
nest_asyncio.apply()  # 启用嵌套异步事件循环支持
```

## 技术特点

### 上下文管理
- **解耦设计**: Model与RequestHandler解耦，通过Context传递上下文
- **用户信息**: 自动提取和设置当前用户信息
- **资源管理**: 支持with语句的上下文管理器

### 数据库访问
- **读写分离**: 支持主从数据库分离
- **连接池**: 使用作用域会话管理连接
- **异步支持**: 使用线程池执行器实现异步数据库操作
- **查询优化**: 针对不同查询类型的COUNT优化

### Redis支持
- **队列操作**: 支持Redis队列的发送和监听
- **异步监听**: 提供同步和异步两种监听方式
- **超时控制**: 支持监听超时设置

### 性能优化
- **懒加载**: 数据库会话按需创建
- **分页限制**: 防止大量数据查询影响性能
- **连接复用**: 通过作用域会话复用连接
