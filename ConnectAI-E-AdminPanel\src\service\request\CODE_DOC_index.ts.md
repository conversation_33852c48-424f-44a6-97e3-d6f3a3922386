# 代码文档 - src/service/request/index.ts

## 文件作用
HTTP请求服务的主入口文件，根据环境配置创建不同的请求实例，支持开发代理和Mock数据。

## 逐行代码解释

### 导入模块 (1-2行)
```typescript
import { getServiceEnvConfig } from '~/.env-config';  // 获取服务环境配置
import { createRequest } from './request';            // 请求实例创建函数
```

### 环境配置获取 (4行)
```typescript
const { url, proxyPattern } = getServiceEnvConfig(import.meta.env);
```
**说明**:
- `getServiceEnvConfig()`: 根据当前环境变量获取服务配置
- `url`: 实际的API服务地址
- `proxyPattern`: 开发环境的代理模式地址
- `import.meta.env`: Vite提供的环境变量对象

### 代理模式检测 (6行)
```typescript
const isHttpProxy = import.meta.env.VITE_HTTP_PROXY === 'Y';
```
**说明**:
- 检查环境变量 `VITE_HTTP_PROXY` 是否为 'Y'
- 用于判断是否启用HTTP代理模式
- 开发环境通常启用代理解决跨域问题

### 请求实例创建 (8行)
```typescript
export const request = createRequest({ baseURL: isHttpProxy ? proxyPattern : url });
```
**说明**:
- 根据代理模式选择不同的baseURL
- `isHttpProxy = true`: 使用代理模式地址 (proxyPattern)
- `isHttpProxy = false`: 使用实际服务地址 (url)
- 导出主要的请求实例供全局使用

### Mock请求实例 (10行)
```typescript
export const mockRequest = createRequest({ baseURL: '/mock' });
```
**说明**:
- 创建专门用于Mock数据的请求实例
- baseURL设置为 '/mock'，通常由开发工具拦截
- 用于开发阶段的数据模拟和接口测试

## 技术特点

### 环境适配
- **多环境支持**: 自动适配开发、测试、生产环境
- **代理模式**: 开发环境支持HTTP代理解决跨域
- **配置驱动**: 通过环境变量控制请求行为

### 请求实例管理
- **主请求实例**: 用于实际API调用的request
- **Mock实例**: 用于开发测试的mockRequest
- **统一接口**: 两个实例使用相同的API接口

### 配置灵活性
- **动态baseURL**: 根据环境动态选择API地址
- **代理切换**: 可通过环境变量快速切换代理模式
- **Mock支持**: 内置Mock数据支持

## 使用场景

### 开发环境
- **本地开发**: 使用代理模式连接后端服务
- **跨域解决**: 通过Vite代理解决开发时的跨域问题
- **Mock测试**: 使用mockRequest进行接口测试

### 生产环境
- **直连模式**: 直接连接生产API服务
- **CDN部署**: 支持CDN等静态部署方式
- **域名配置**: 通过环境变量配置不同的API域名

### 测试环境
- **环境隔离**: 不同测试环境使用不同的API地址
- **数据模拟**: 使用Mock数据进行功能测试
- **接口调试**: 方便的接口调试和测试

## 配置说明

### 环境变量
- **VITE_HTTP_PROXY**: 控制是否使用HTTP代理
  - 'Y': 启用代理模式，使用proxyPattern
  - 其他值: 直连模式，使用实际url
- **其他环境变量**: 通过.env-config获取服务配置

### 代理配置
- **开发代理**: 通常在vite.config.ts中配置
- **代理模式**: 解决开发环境的跨域问题
- **路径重写**: 支持API路径的重写和转发

## 扩展性
- **多实例**: 可以创建更多特定用途的请求实例
- **拦截器**: 在createRequest中配置请求/响应拦截器
- **认证**: 统一的认证token处理
- **错误处理**: 全局的错误处理机制

## 与其他模块的关系
- **依赖**: .env-config (环境配置)、./request (请求创建)
- **被使用**: 各个API服务模块、页面组件
- **配置**: vite.config.ts (代理配置)、环境变量文件
